<script setup lang="ts">
  import {
    useRoute,
    useRouter,
    type RouteLocationNormalizedLoaded,
    type Router,
  } from 'vue-router'
  // import useApi from '../../composables/useApi.js'
  import {PATH_NAME} from '../../defined/const.js'
  // import ChangePassword from './ChangePassword.vue'

  // Type definitions
  type ValidationCheck = {
    pass: boolean
    errMsg: string
  }

  type ValidationResult = {
    email: ValidationCheck
    password: ValidationCheck
  }

  type LoginData = {
    user_id: string
    password: string
  }

  // // Store instances
  // const auth = useAuthStore()
  // const languageStore = useLanguageStore()

  // // Router and route
  const route: RouteLocationNormalizedLoaded = useRoute()
  const router: Router = useRouter()

  // // Reactive state
  // // const openChangePasswordModal: Ref<boolean> = ref(false)
  // const tempToken: Ref<string | null> = ref(null)
  // const email: Ref<string> = ref('')
  // const password: Ref<string> = ref('')
  // const saveLoginInfoFlag: Ref<boolean> = ref(false)
  // const errorMsg: Ref<string> = ref('')

  // // Locale
  // const { t: translate } = useLocale()

  // // Constants
  // const errClass: string = 'ime-dis err'
  // const { VITE_LOCALSTORAGE_LOGIN_INFO_LABEL } = import.meta.env

  // Initialize saved login data
  // if (localStorage[VITE_LOCALSTORAGE_LOGIN_INFO_LABEL]) {
  //   try {
  //     const loginData: LoginData = aesDecrypt(localStorage[VITE_LOCALSTORAGE_LOGIN_INFO_LABEL])

  //     if (loginData?.user_id && loginData.password) {
  //       email.value = loginData.user_id
  //       password.value = loginData.password
  //       saveLoginInfoFlag.value = true
  //     }
  //   } catch (error) {
  //     console.warn('Failed to decrypt saved login data:', error)
  //   }
  // }

  // Validation computed properties
  // const checkInput: ComputedRef<ValidationResult> = computed(() => {
  //   const check: ValidationResult = {
  //     email: { pass: false, errMsg: 'メールアドレスを確認してください。' },
  //     password: { pass: false, errMsg: 'パスワードを確認してください。' }
  //   }

  //   if (PATTERN.EMAIL.test(email.value)) {
  //     check.email.pass = true
  //     check.email.errMsg = ''
  //   }

  //   if (password.value !== '') {
  //     check.password.pass = true
  //     check.password.errMsg = ''
  //   }

  //   return check
  // })

  // const isLoginDisabled: ComputedRef<boolean> = computed(() => {
  //   // return !(checkInput.value.email.pass && checkInput.value.password.pass)
  // })

  // Simplified login function (no API call, just validation and redirect)
  // const sendRequest = async (): Promise<void> => {
  //   try {
  //     // Clear any previous errors
  //     errorMsg.value = ''
  //     tempToken.value = null
  //     openChangePasswordModal.value = false

  //     // Create login data object
  //     const loginData: LoginData = {
  //       user_id: email.value,
  //       password: password.value
  //     }

  //     // Set mock token and user data
  //     auth.setToken('mock-token-for-development')
  //     auth.setNickname(email.value.split('@')[0] || 'User')

  //     // Handle save login info
  //     if (saveLoginInfoFlag.value) {
  //       localStorage[import.meta.env.VITE_LOCALSTORAGE_LOGIN_INFO_LABEL] = aesEncrypt(loginData)
  //     } else {
  //       localStorage.removeItem(import.meta.env.VITE_LOCALSTORAGE_LOGIN_INFO_LABEL)
  //     }

  //     // Redirect to the intended page or top page
  //     await router.replace(route.redirectedFrom?.path ?? PATH_NAME.TOP)
  //   } catch (error) {
  //     console.error('Login error:', error)
  //     errorMsg.value = 'ログイン処理中にエラーが発生しました。'
  //   }
  // }

  const handleLogin = () => {
    console.log('Logging in...')
    router.replace(route.redirectedFrom?.path ?? PATH_NAME.TOP)
  }

  const navigateToReminder = () => {
    router.push(PATH_NAME.REMINDER)
  }
</script>
<template>
  <h2 class="page-ttl">
    <p class="ttl">ログイン</p>
    <p class="sub">login</p>
  </h2>
  <div class="container">
    <section id="login-form">
      <form>
        <div class="id-pass-err">
          <span class="err-txt">
            ログインIDまたはパスワードが正しくありません。
          </span>
        </div>
        <table class="tbl-login">
          <tbody>
            <tr>
              <th>ログインID<em class="req">※必須</em></th>
              <td>
                <input
                  type="text"
                  class="ime-dis err"
                  placeholder="8～14文字の半角英数字"
                  required
                />
                <p class="err-txt">未入力です</p>
              </td>
            </tr>
            <tr>
              <th>パスワード<em class="req">※必須</em></th>
              <td>
                <input
                  type="password"
                  class="ime-dis err"
                  placeholder="8～14文字の半角英数字"
                  required
                />
                <p class="err-txt">未入力です</p>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="check-idpass">
          <label>
            <input type="checkbox" class="checkbox-input" />
            <span class="checkbox-parts">ID・パスワードを保存</span>
          </label>
        </div>
        <div class="forget-pass">
          <a @click="navigateToReminder">パスワードを忘れた方はコチラ</a>
        </div>

        <div class="rule">
          <p class="tit-rule">入札会参加要項</p>
          <embed
            src="/pdf/sample.pdf"
            type="application/pdf"
            width="100%"
            height="150"
          />
          <div class="rule-check">
            <label for="rule-chk">
              <input
                type="checkbox"
                id="rule-chk"
                class="checkbox-input"
                required
              />
              <span class="checkbox-parts">参加規約に同意する</span>
            </label>
          </div>
        </div>
        <div class="btn-form">
          <input
            type="button"
            id="sbm-login"
            @click="handleLogin"
            value="ログイン"
          />
        </div>
      </form>
      <div class="request">
        <a class="register-btt" @click="() => router.push(PATH_NAME.REGISTER)"
          >新規会員登録（詳細）</a
        >
        <span class="mx-2">|</span>
        <a
          class="register-btt"
          @click="() => router.push(PATH_NAME.COGNITO_REGISTER)"
          >簡単登録（Cognito）</a
        >
        <p>※商品の価格を見るには会員登録が必要です。</p>
      </div>
    </section>
  </div>
</template>

<style lang="css">
  .register-btt {
    cursor: pointer;
  }
</style>
