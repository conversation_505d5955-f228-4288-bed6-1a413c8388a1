const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const {
  AdminAddUserToGroupCommand,
  AdminCreateUserCommand,
  AdminSetUserPasswordCommand,
  AdminUpdateUserAttributesCommand,
  CognitoIdentityProviderClient,
} = require('@aws-sdk/client-cognito-identity-provider')

const pool = new PgPool()
const cognitoClient = new CognitoIdentityProviderClient({})

/**
 * Validates user input and checks for existing email
 * DATABASE TEMPORARILY DISABLED FOR TESTING
 */
async function validateInput(params, tenant) {
  const registerData = params.registerData

  // COMMENTED OUT: Database check for duplicate email (DB is offline)
  // if (registerData?.email) {
  //   const members = await pool.query('SELECT * FROM "f_get_member_by_email"($1,$2);', [
  //     tenant.tenant_no,
  //     registerData.email,
  //   ]);
  //
  //   if (members && members.length > 0) {
  //     throw {
  //       status: 400,
  //       name: 'Email Already Exists',
  //       message: 'このメールアドレスは既に登録されています。',
  //     };
  //   }
  // }

  // Basic validation without database
  if (!registerData?.email) {
    throw {
      status: 400,
      name: 'Validation Error',
      message: 'メールアドレスが必要です。',
    }
  }

  if (!registerData?.memberName) {
    throw {
      status: 400,
      name: 'Validation Error',
      message: '会員名が必要です。',
    }
  }

  // Validate password confirmation
  if (registerData.password !== registerData.passwordConfirm) {
    throw {
      status: 400,
      name: 'Password Mismatch',
      message: 'パスワードと確認用パスワードが一致しません。',
    }
  }
}

/**
 * Creates a new user in the Cognito user pool
 */
async function createCognitoUser(email, password, memberData, tenantNo) {
  const command = new AdminCreateUserCommand({
    UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
    Username: email,
    UserAttributes: [
      {Name: 'email', Value: email},
      {Name: 'email_verified', Value: 'true'},
      {Name: 'custom:member_name', Value: memberData.memberName || ''},
      {Name: 'custom:language_code', Value: memberData.language || 'ja'},
    ],
    TemporaryPassword: password,
    MessageAction: 'SUPPRESS',
  })

  const createUserResult = await cognitoClient.send(command)
  console.log('User created in Cognito:', createUserResult)
  return createUserResult
}

/**
 * Sets the user's password to permanent in Cognito
 */
async function setCognitoPassword(username, password) {
  const command = new AdminSetUserPasswordCommand({
    UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
    Username: username,
    Password: password,
    Permanent: true,
  })
  await cognitoClient.send(command)
  console.log('Password set to permanent for user:', username)
}

/**
 * Adds the Cognito user to the tenant-specific group
 */
async function addUserToCognitoGroup(username, tenantNo) {
  const groupName = `tenant-id:${tenantNo}`
  const command = new AdminAddUserToGroupCommand({
    UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
    Username: username,
    GroupName: groupName,
  })
  await cognitoClient.send(command)
  console.log(`User ${username} added to group ${groupName}`)
  return groupName
}

/**
 * Creates member record in database
 */
async function createMemberInDb(memberData, tenantNo, password) {
  const freeField = {
    memberName: memberData.memberName,
    language: memberData.language || 'ja',
    email: memberData.email,
    // Add other member fields as needed
  }

  const result = await pool.query(
    'SELECT * FROM "f_create_member_with_cognito"($1,$2,$3);',
    [tenantNo, JSON.stringify(freeField), memberData.email]
  )

  console.log('Member created in database:', result)
  return result[0]
}

/**
 * Updates Cognito user with member_no and user_no
 */
async function updateCognitoUserAttributes(username, memberNo, userNo) {
  const command = new AdminUpdateUserAttributesCommand({
    UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
    Username: username,
    UserAttributes: [
      {Name: 'custom:member_no', Value: memberNo.toString()},
      {Name: 'custom:user_no', Value: userNo.toString()},
    ],
  })
  await cognitoClient.send(command)
  console.log(
    `Updated Cognito user ${username} with member_no: ${memberNo}, user_no: ${userNo}`
  )
}

/**
 * Main registration logic
 */
async function registerMember(e) {
  const params = e.body
  const header = e.headers
  const base = new Base(pool, params.languageCode)

  await base.startRequest(e)

  // Get tenant information from origin
  const tenant = await base.checkOrigin(header.origin || header.Origin)
  console.log('tenant:', tenant)

  // Validate input
  await validateInput(params, tenant)

  const registerData = params.registerData
  const email = registerData.email
  const password = registerData.password

  let createdMember = null
  let cognitoUsername = null

  try {
    // 1. Create Cognito user
    const createUserResult = await createCognitoUser(
      email,
      password,
      registerData,
      tenant.tenant_no
    )
    cognitoUsername = createUserResult.User.Username

    // 2. Set permanent password
    await setCognitoPassword(cognitoUsername, password)

    // 3. Add to tenant group
    const groupName = await addUserToCognitoGroup(
      cognitoUsername,
      tenant.tenant_no
    )

    // 4. Create member record in database
    createdMember = await createMemberInDb(
      registerData,
      tenant.tenant_no,
      password
    )

    // 5. Update Cognito user with member_no and user_no
    await updateCognitoUserAttributes(
      cognitoUsername,
      createdMember.member_no,
      createdMember.user_no
    )

    return {
      message: 'Member registered successfully',
      memberNo: createdMember.member_no,
      email: email,
      tenantId: tenant.tenant_no,
      groupName: groupName,
    }
  } catch (error) {
    // Rollback logic if needed
    console.error('Registration error:', error)
    throw error
  }
}

/**
 * Lambda handler function
 */
exports.handle = (e, ctx, cb) => {
  ctx.callbackWaitsForEmptyEventLoop = false
  console.log('cognito-register-member:', e)

  registerMember(e)
    .then(result => Base.createSuccessResponse(cb, result))
    .catch(error => {
      console.error('Registration failed:', error)
      const errorResponse = {
        status: error.status || 500,
        name: error.name || 'Registration Error',
        message: error.message || '登録に失敗しました。',
      }
      return Base.createErrorResponse(cb, errorResponse)
    })
}
